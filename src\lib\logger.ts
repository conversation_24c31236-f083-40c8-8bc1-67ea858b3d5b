// lib/logger.ts
import { nanoid } from "nanoid";
import pino from "pino";

const isDevelopment = process.env.NODE_ENV === "development";

export const logger = pino({
  level: process.env.LOG_LEVEL || "info",
  transport: isDevelopment
    ? {
        target: "pino-pretty",
        options: {
          colorize: true,
          translateTime: "SYS:standard",
          ignore: "pid,hostname",
        },
      }
    : undefined,
  formatters: {
    level: (label) => {
      return { level: label.toUpperCase() };
    },
  },
  hooks: {
    logMethod(inputArgs, method) {
      const correlationId: any = (global as any).correlationId || nanoid();
      const args = [...inputArgs] as any[];

      if (typeof args[0] === "object" && args[0] !== null) {
        args[0] = {
          correlationId,
          ...(args[0] as Record<string, any>),
        };
      } else {
        args.unshift({ correlationId });
      }

      method.apply(this, args as any);
    },
  },
});

// Logging utilities for Next.js API routes
export function logRequest(req: Request, correlationId?: string) {
  const id = correlationId || nanoid();
  (global as any).correlationId = id;

  logger.info({
    type: "http-request",
    method: req.method,
    url: req.url,
    correlationId: id,
    userAgent: req.headers.get("user-agent"),
  });

  return {
    correlationId: id,
    startTime: Date.now(),
  };
}

export function logResponse(
  req: Request,
  response: Response,
  correlationId: string,
  startTime: number
) {
  const duration = Date.now() - startTime;

  logger.info({
    type: "http-response",
    method: req.method,
    url: req.url,
    statusCode: response.status,
    duration: `${duration}ms`,
    correlationId,
  });

  // Cleanup
  delete (global as any).correlationId;
}

// Helper function to wrap API route handlers with logging
export function withLogging<T extends any[]>(
  handler: (req: Request, ...args: T) => Promise<Response>
) {
  return async (req: Request, ...args: T): Promise<Response> => {
    const { correlationId, startTime } = logRequest(req);

    try {
      const response = await handler(req, ...args);
      logResponse(req, response, correlationId, startTime);
      return response;
    } catch (error) {
      logger.error({
        type: "http-error",
        method: req.method,
        url: req.url,
        error: error instanceof Error ? error.message : String(error),
        correlationId,
      });

      // Cleanup on error
      delete (global as any).correlationId;
      throw error;
    }
  };
}
